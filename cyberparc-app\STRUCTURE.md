# 📁 Structure de l'Application CyberParc

## Vue d'ensemble
Cette application a été restructurée pour une meilleure organisation et facilité de déploiement.

## 🗂️ Structure des Dossiers

```
cyberparc-app/
├── 📁 config/                 # Configuration de l'application
│   ├── database.php           # Configuration BDD et fonctions utilitaires
│   └── installed.lock         # Fichier de verrouillage (créé après installation)
│
├── 📁 pages/                  # Pages de l'application
│   ├── dashboard.php          # Tableau de bord principal (ex: accueil.php)
│   ├── register.php           # Page d'inscription
│   ├── forgot-password.php    # Récupération mot de passe
│   ├── notifications.php      # Gestion des notifications
│   ├── ajouter_entreprise.php # Ajout d'entreprise
│   ├── modifier_entreprise.php# Modification d'entreprise
│   ├── details_entreprise.php # Détails d'une entreprise
│   ├── ajouter_facture.php    # Ajout de facture
│   ├── modifier_facture.php   # Modification de facture
│   ├── supprimer_facture.php  # Suppression de facture
│   ├── ajouter_virement.php   # Ajout de virement
│   └── ... (autres pages)
│
├── 📁 assets/                 # Ressources statiques
│   ├── 📁 images/             # Images (logos, icônes)
│   │   ├── logo1.jpg          # Logo principal
│   │   └── icone.ico          # Favicon
│   ├── 📁 css/                # Feuilles de style (vide pour l'instant)
│   └── 📁 js/                 # Scripts JavaScript (vide pour l'instant)
│
├── 📁 includes/               # Fichiers inclus
│   └── logout.php             # Script de déconnexion
│
├── 📁 database/               # Scripts de base de données
│   └── stage (2).sql          # Schéma de base de données
│
├── 📁 vendor/                 # Dépendances Composer (créé après composer install)
│
├── 📄 index.php               # Point d'entrée principal (page de connexion)
├── 📄 install.php             # Script d'installation automatique
├── 📄 composer.json           # Dépendances PHP
├── 📄 composer.lock           # Verrouillage des versions
├── 📄 .htaccess               # Configuration Apache
├── 📄 README.md               # Documentation utilisateur
├── 📄 INSTALLATION.md         # Guide d'installation détaillé
└── 📄 STRUCTURE.md            # Ce fichier
```

## 🔄 Flux de l'Application

### 1. Point d'Entrée
- **index.php** : Page de connexion principale
- Vérifie si l'utilisateur est connecté
- Redirige vers le dashboard si connecté
- Affiche le formulaire de connexion sinon

### 2. Authentification
- Utilise les sessions PHP
- Mots de passe hachés avec `password_hash()`
- Redirection automatique après connexion

### 3. Navigation
- **dashboard.php** : Page principale après connexion
- Liens vers toutes les fonctionnalités
- Gestion des entreprises, factures, virements

### 4. Configuration
- **config/database.php** : Configuration centralisée
- Fonctions utilitaires (connexion PDO, sécurité)
- Constantes de l'application

## 🔧 Fonctionnalités Principales

### Gestion des Entreprises
- Ajout, modification, suppression
- Upload de logos
- Activation/désactivation
- Détails avec comptabilité

### Gestion des Factures
- Création et suivi des factures
- Calcul automatique des échéances
- Statuts de paiement
- Alertes automatiques

### Gestion des Virements
- Enregistrement des paiements
- Liaison avec les factures
- Historique des transactions

### Notifications
- Alertes automatiques
- Gestion des notifications lues/non lues
- Interface de gestion

## 🛠️ Technologies Utilisées

- **Backend** : PHP 7.4+
- **Base de données** : MySQL 5.7+
- **Frontend** : HTML5, CSS3, JavaScript vanilla
- **Dépendances** : PHPMailer (pour les emails)
- **Serveur web** : Apache (avec .htaccess)

## 🔒 Sécurité

### Mesures Implémentées
- Hachage des mots de passe
- Protection CSRF (sessions)
- Validation et échappement des données
- Protection des fichiers sensibles (.htaccess)
- Headers de sécurité HTTP

### Fichiers Protégés
- `/config/` : Configuration sensible
- `/database/` : Scripts SQL
- `composer.json/lock` : Dépendances
- `*.md` : Documentation

## 📦 Déploiement

### Installation Automatique
1. Extraire l'archive sur le serveur
2. Accéder à `install.php`
3. Suivre l'assistant d'installation
4. Supprimer `install.php` après installation

### Installation Manuelle
1. Configurer la base de données
2. Modifier `config/database.php`
3. Importer `database/stage (2).sql`
4. Créer le premier compte utilisateur

## 🔄 Maintenance

### Sauvegardes
- Base de données : Export SQL régulier
- Fichiers : Sauvegarde du dossier complet
- Logs : Surveillance des erreurs PHP/Apache

### Mises à jour
- Vérifier les dépendances Composer
- Tester sur environnement de développement
- Sauvegarder avant mise en production

## 📝 Notes pour les Développeurs

### Conventions
- Noms de fichiers en minuscules avec underscores
- Variables en camelCase
- Constantes en MAJUSCULES
- Commentaires en français

### Extensions Possibles
- API REST pour mobile
- Interface d'administration avancée
- Rapports et statistiques
- Intégration comptable
- Multi-tenancy (plusieurs cyber parcs)

---
**Version** : 1.0.0  
**Dernière mise à jour** : Août 2025
