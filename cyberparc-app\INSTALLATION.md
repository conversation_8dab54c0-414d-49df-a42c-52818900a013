# 🏢 CyberParc Management System - Guide d'Installation

## 📋 Prérequis

Avant d'installer l'application, assurez-vous d'avoir :

- **Serveur Web** : Apache ou Nginx
- **PHP** : Version 7.4 ou supérieure
- **MySQL** : Version 5.7 ou supérieure (ou MariaDB 10.2+)
- **Extensions PHP requises** :
  - PDO
  - PDO_MySQL
  - mbstring
  - openssl (pour l'envoi d'emails)

## 🚀 Installation Rapide

### Étape 1 : Téléchargement
1. Téléchargez le fichier `cyberparc-app.zip`
2. Extrayez le contenu dans le dossier de votre serveur web (ex: `htdocs`, `www`, `public_html`)

### Étape 2 : Configuration de la Base de Données

#### Option A : Installation Automatique (Recommandée)
1. Ouvrez votre navigateur
2. Allez sur : `http://votre-domaine.com/cyberparc-app/install.php`
3. <PERSON><PERSON>z les instructions à l'écran

#### Option B : Installation Manuelle
1. C<PERSON>ez une base de données MySQL nommée `stage`
2. Importez le fichier `database/stage (2).sql` dans votre base de données :
   ```sql
   mysql -u root -p stage < database/stage\ \(2\).sql
   ```

### Étape 3 : Configuration de l'Application
1. Ouvrez le fichier `config/database.php`
2. Modifiez les paramètres de connexion à la base de données :
   ```php
   define('DB_HOST', 'localhost');     // Adresse du serveur MySQL
   define('DB_NAME', 'stage');         // Nom de la base de données
   define('DB_USER', 'root');          // Nom d'utilisateur MySQL
   define('DB_PASS', '');              // Mot de passe MySQL
   ```

### Étape 4 : Configuration des Permissions
Assurez-vous que les dossiers suivants sont accessibles en écriture :
- `assets/images/` (pour les logos des entreprises)
- `includes/` (pour les fichiers temporaires)

### Étape 5 : Premier Accès
1. Ouvrez votre navigateur
2. Allez sur : `http://votre-domaine.com/cyberparc-app/`
3. Créez votre premier compte administrateur

## ⚙️ Configuration Avancée

### Configuration Email (Optionnelle)
Pour activer la récupération de mot de passe par email, modifiez dans `config/database.php` :
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'votre-mot-de-passe-app');
define('SMTP_FROM_EMAIL', '<EMAIL>');
```

### Sécurité
1. Changez les mots de passe par défaut de la base de données
2. Configurez HTTPS pour votre site
3. Limitez l'accès au dossier `config/` via `.htaccess`

## 🔧 Dépannage

### Problème : "Erreur de connexion à la base de données"
- Vérifiez les paramètres dans `config/database.php`
- Assurez-vous que MySQL est démarré
- Vérifiez que la base de données `stage` existe

### Problème : "Page blanche"
- Activez l'affichage des erreurs PHP
- Vérifiez les logs d'erreur du serveur
- Assurez-vous que toutes les extensions PHP sont installées

### Problème : "Impossible d'uploader des images"
- Vérifiez les permissions du dossier `assets/images/`
- Augmentez la limite `upload_max_filesize` dans php.ini

## 📞 Support

Pour toute question ou problème :
1. Consultez d'abord ce guide
2. Vérifiez les logs d'erreur
3. Contactez votre administrateur système

## 📝 Notes Importantes

- **Sauvegarde** : Effectuez régulièrement des sauvegardes de votre base de données
- **Mises à jour** : Gardez PHP et MySQL à jour pour la sécurité
- **Performances** : Pour de meilleures performances, activez le cache PHP (OPcache)

---

**Version** : 1.0.0  
**Dernière mise à jour** : Août 2025
