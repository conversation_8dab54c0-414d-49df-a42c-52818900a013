<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

if ($_SERVER["REQUEST_METHOD"] === "POST") {
  $nom = $_POST['nom'];
  $chef = $_POST['chef'];
  $numero = $_POST['numero'];
  $email = $_POST['email'];
  $local = $_SESSION['utilisateur']['local'];

  $logo = null;

  // Gérer le logo s'il est présent
  if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
    $tmpName = $_FILES['logo']['tmp_name'];
    $originalName = basename($_FILES['logo']['name']);
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $allowed = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    if (in_array($extension, $allowed)) {
      // Nom de fichier unique
      $logo = uniqid() . '.' . $extension;

      // Créer dossier s’il n’existe pas
      $uploadDir = 'uploads/logos/';
      if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
      }

      // Déplacement du fichier
      move_uploaded_file($tmpName, $uploadDir . $logo);
    }
  }

  // Insertion avec logo
  $stmt = $pdo->prepare("INSERT INTO entreprises (nom, chef, numero, email, local, logo, active) VALUES (?, ?, ?, ?, ?, ?, 1)");
  $stmt->execute([$nom, $chef, $numero, $email, $local, $logo]);

  // Redirection avec message de succès
  header("Location: accueil.php?success=entreprise_ajoutee");
  exit;
}