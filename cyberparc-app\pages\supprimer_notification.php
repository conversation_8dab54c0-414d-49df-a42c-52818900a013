<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['utilisateur']) || !isset($_SESSION['utilisateur']['id'])) {
  http_response_code(403);
  echo json_encode(["success" => false, "error" => "Non autorisé"]);
  exit;
}

$utilisateurId = $_SESSION['utilisateur']['id'];
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['id'])) {
  http_response_code(400);
  echo json_encode(["success" => false, "error" => "ID de notification manquant"]);
  exit;
}

$notificationId = intval($input['id']);

try {
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ? AND utilisateur_id = ?");
  $stmt->execute([$notificationId, $utilisateurId]);

  if ($stmt->rowCount() > 0) {
    echo json_encode(["success" => true, "message" => "Notification supprimée avec succès"]);
  } else {
    echo json_encode(["success" => false, "error" => "Notification non trouvée"]);
  }
} catch (PDOException $e) {
  error_log("Erreur BDD supprimer_notification: " . $e->getMessage());
  http_response_code(500);
  echo json_encode(["success" => false, "error" => "Erreur base de données : " . $e->getMessage()]);
}
?>
