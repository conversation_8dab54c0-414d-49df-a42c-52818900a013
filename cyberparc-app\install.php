<?php
/**
 * Script d'installation automatique pour CyberParc Management System
 */

session_start();

// Vérifier si l'installation est déjà effectuée
if (file_exists('config/installed.lock')) {
    die('L\'application est déjà installée. Supprimez le fichier config/installed.lock pour réinstaller.');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Traitement des étapes
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Vérification des prérequis
            $step = 2;
            break;
            
        case 2:
            // Configuration de la base de données
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'stage';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            try {
                // Test de connexion
                $pdo = new PDO("mysql:host=$db_host;charset=utf8", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Créer la base de données si elle n'existe pas
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8 COLLATE utf8_general_ci");
                $pdo->exec("USE `$db_name`");
                
                // Importer le schéma de base de données
                $sql = file_get_contents('database/stage (2).sql');
                if ($sql) {
                    // Diviser le SQL en requêtes individuelles
                    $queries = explode(';', $sql);
                    foreach ($queries as $query) {
                        $query = trim($query);
                        if (!empty($query)) {
                            $pdo->exec($query);
                        }
                    }
                }
                
                // Sauvegarder la configuration
                $config_content = file_get_contents('config/database.php');
                $config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $config_content);
                $config_content = str_replace("define('DB_NAME', 'stage');", "define('DB_NAME', '$db_name');", $config_content);
                $config_content = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$db_user');", $config_content);
                $config_content = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$db_pass');", $config_content);
                
                file_put_contents('config/database.php', $config_content);
                
                $_SESSION['install_data'] = [
                    'db_host' => $db_host,
                    'db_name' => $db_name,
                    'db_user' => $db_user,
                    'db_pass' => $db_pass
                ];
                
                $step = 3;
                $success = 'Base de données configurée avec succès !';
                
            } catch (Exception $e) {
                $error = 'Erreur de configuration de la base de données : ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Création du compte administrateur
            $nom = trim($_POST['nom'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $telephone = trim($_POST['telephone'] ?? '');
            $gouvernorat = trim($_POST['gouvernorat'] ?? '');
            $local = trim($_POST['local'] ?? '');
            
            if (empty($nom) || empty($email) || empty($password) || empty($telephone) || empty($gouvernorat) || empty($local)) {
                $error = 'Veuillez remplir tous les champs.';
            } elseif (strlen($password) < 6) {
                $error = 'Le mot de passe doit contenir au moins 6 caractères.';
            } else {
                try {
                    require_once 'config/database.php';
                    $pdo = getPDOConnection();
                    
                    // Créer le compte administrateur
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO utilisateurs (nom, email, motdepasse, telephone, gouvernorat, local) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$nom, $email, $hashedPassword, $telephone, $gouvernorat, $local]);
                    
                    // Marquer l'installation comme terminée
                    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                    
                    $step = 4;
                    $success = 'Installation terminée avec succès !';
                    
                } catch (Exception $e) {
                    $error = 'Erreur lors de la création du compte : ' . $e->getMessage();
                }
            }
            break;
    }
}

// Fonction pour vérifier les prérequis
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'Extension PDO' => extension_loaded('pdo'),
        'Extension PDO MySQL' => extension_loaded('pdo_mysql'),
        'Extension mbstring' => extension_loaded('mbstring'),
        'Dossier config/ accessible en écriture' => is_writable('config/'),
        'Dossier assets/images/ accessible en écriture' => is_writable('assets/images/'),
    ];
    
    return $requirements;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - CyberParc Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .install-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .steps {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: white;
        }

        .step.active {
            background: #667eea;
        }

        .step.completed {
            background: #28a745;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: 500;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="tel"],
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .alert-success {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
        }

        .requirements {
            list-style: none;
        }

        .requirements li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .requirements .ok {
            color: #28a745;
        }

        .requirements .error {
            color: #dc3545;
        }

        @media (max-width: 600px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="header">
            <h1>🏢 Installation CyberParc</h1>
            <p>Assistant d'installation automatique</p>
        </div>

        <div class="steps">
            <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">1</div>
            <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : '' ?>">2</div>
            <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : '' ?>">3</div>
            <div class="step <?= $step >= 4 ? 'active' : '' ?>">4</div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                ❌ <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                ✅ <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($step === 1): ?>
            <h2>Étape 1 : Vérification des prérequis</h2>
            <ul class="requirements">
                <?php foreach (checkRequirements() as $req => $status): ?>
                    <li class="<?= $status ? 'ok' : 'error' ?>">
                        <?= $status ? '✅' : '❌' ?> <?= $req ?>
                    </li>
                <?php endforeach; ?>
            </ul>
            
            <?php if (array_product(checkRequirements())): ?>
                <form method="POST">
                    <button type="submit" class="btn">Continuer →</button>
                </form>
            <?php else: ?>
                <p style="color: #dc3545; margin-top: 1rem;">
                    Veuillez corriger les problèmes ci-dessus avant de continuer.
                </p>
            <?php endif; ?>

        <?php elseif ($step === 2): ?>
            <h2>Étape 2 : Configuration de la base de données</h2>
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_host">Serveur :</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Base de données :</label>
                        <input type="text" id="db_name" name="db_name" value="stage" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_user">Utilisateur :</label>
                        <input type="text" id="db_user" name="db_user" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Mot de passe :</label>
                        <input type="password" id="db_pass" name="db_pass">
                    </div>
                </div>
                <button type="submit" class="btn">Configurer la base de données</button>
            </form>

        <?php elseif ($step === 3): ?>
            <h2>Étape 3 : Compte administrateur</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="nom">Nom complet :</label>
                    <input type="text" id="nom" name="nom" required>
                </div>
                <div class="form-group">
                    <label for="email">Email :</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Mot de passe :</label>
                    <input type="password" id="password" name="password" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="telephone">Téléphone :</label>
                    <input type="tel" id="telephone" name="telephone" required>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="gouvernorat">Gouvernorat :</label>
                        <select id="gouvernorat" name="gouvernorat" required>
                            <option value="">Sélectionner...</option>
                            <option value="Tunis">Tunis</option>
                            <option value="Ariana">Ariana</option>
                            <option value="Ben Arous">Ben Arous</option>
                            <option value="Manouba">Manouba</option>
                            <option value="Nabeul">Nabeul</option>
                            <option value="Médenine">Médenine</option>
                            <!-- Ajoutez d'autres gouvernorats selon vos besoins -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="local">Nom du local :</label>
                        <input type="text" id="local" name="local" required>
                    </div>
                </div>
                <button type="submit" class="btn">Créer le compte</button>
            </form>

        <?php elseif ($step === 4): ?>
            <h2>🎉 Installation terminée !</h2>
            <p>Votre application CyberParc est maintenant prête à être utilisée.</p>
            <div style="margin: 2rem 0;">
                <a href="index.php" class="btn" style="text-decoration: none; display: block; text-align: center;">
                    Accéder à l'application
                </a>
            </div>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-top: 1rem;">
                <h3>Prochaines étapes :</h3>
                <ul style="margin-left: 1.5rem; margin-top: 0.5rem;">
                    <li>Connectez-vous avec le compte créé</li>
                    <li>Ajoutez vos premières entreprises</li>
                    <li>Configurez les paramètres email (optionnel)</li>
                    <li>Supprimez le fichier install.php pour la sécurité</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
