<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
  die("Méthode non autorisée.");
}

try {
  // Vérification des champs obligatoires
  if (empty($_POST['entreprise_id']) || empty($_POST['facture_id'])) {
    die("Paramètres manquants.");
  }

  $entreprise_id = intval($_POST['entreprise_id']);
  $facture_id = intval($_POST['facture_id']);

  // Validation des données
  if ($entreprise_id <= 0) {
    die("ID d'entreprise invalide.");
  }

  if ($facture_id <= 0) {
    die("ID de facture invalide.");
  }

  // Connexion à la base de données
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  // Vérifier si l'entreprise existe et est active
  $checkEntreprise = $pdo->prepare("SELECT id, nom, active FROM entreprises WHERE id = ?");
  $checkEntreprise->execute([$entreprise_id]);
  $entreprise = $checkEntreprise->fetch(PDO::FETCH_ASSOC);

  if (!$entreprise) {
    die("Entreprise non trouvée.");
  }

  if ($entreprise['active'] != 1) {
    die("Impossible de supprimer une facture d'une entreprise inactive.");
  }

  // Récupérer les détails de la facture avant suppression
  $getFacture = $pdo->prepare("SELECT numFact, Montant FROM facture WHERE id = ? AND entreprise_id = ?");
  $getFacture->execute([$facture_id, $entreprise_id]);
  $facture = $getFacture->fetch(PDO::FETCH_ASSOC);

  if (!$facture) {
    die("Facture non trouvée.");
  }

  // Supprimer la facture
  $stmt = $pdo->prepare("DELETE FROM facture WHERE id = ? AND entreprise_id = ?");
  $result = $stmt->execute([$facture_id, $entreprise_id]);

  if ($result) {
    error_log("Facture supprimée - ID: {$facture_id}, Numéro: {$facture['numFact']}, Entreprise: {$entreprise['nom']}, Montant: {$facture['Montant']} DT");

    header("Location: details_entreprise.php?id={$entreprise_id}&success=facture_supprimee");
    exit;
  } else {
    die("Erreur lors de la suppression de la facture.");
  }
} catch (PDOException $e) {
  error_log("Erreur BDD suppression facture: " . $e->getMessage());
  die("Erreur base de données : " . $e->getMessage());
} catch (Exception $e) {
  error_log("Erreur générale suppression facture: " . $e->getMessage());
  die("Erreur système : " . $e->getMessage());
}
?>
