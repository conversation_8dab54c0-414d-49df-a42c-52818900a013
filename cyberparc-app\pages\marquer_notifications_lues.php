<?php
session_start();
header('Content-Type: application/json');

try {
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  $utilisateurId = isset($_SESSION['utilisateur']['id']) ? $_SESSION['utilisateur']['id'] : null;

  if (!$utilisateurId) {
    echo json_encode(['success' => false, 'error' => 'Utilisateur non connecté']);
    exit;
  }

  // Lire le contenu JSON de la requête
  $input = json_decode(file_get_contents('php://input'), true);

  if (isset($input['action']) && $input['action'] === 'mark_as_read') {
    // Marquer toutes les notifications non lues comme lues
    $stmt = $pdo->prepare("UPDATE notifications SET vu = 1 WHERE utilisateur_id = ? AND vu = 0");
    $stmt->execute([$utilisateurId]);

    echo json_encode(['success' => true, 'message' => 'Notifications marquées comme lues']);
  } else {
    echo json_encode(['success' => false, 'error' => 'Action non valide']);
  }
} catch (PDOException $e) {
  echo json_encode(['success' => false, 'error' => 'Erreur base de données: ' . $e->getMessage()]);
} catch (Exception $e) {
  echo json_encode(['success' => false, 'error' => 'Erreur: ' . $e->getMessage()]);
}