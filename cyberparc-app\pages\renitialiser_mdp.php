<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur base de données : " . $e->getMessage());
}

// Vérification du token dans l'URL
if (!isset($_GET['token']) || empty($_GET['token'])) {
    die("Token manquant dans l'URL.");
}

$token = trim($_GET['token']);

// Log pour debug
error_log("Token reçu: " . $token);
error_log("Longueur du token: " . strlen($token));

// Recherche utilisateur avec token valide
$stmt = $pdo->prepare("SELECT *, reset_expire FROM utilisateurs WHERE reset_token = ?");
$stmt->execute([$token]);
$utilisateur = $stmt->fetch(PDO::FETCH_ASSOC);

// Debug: afficher les informations
if ($utilisateur) {
    error_log("Utilisateur trouvé: " . $utilisateur['email']);
    error_log("Token en BDD: " . $utilisateur['reset_token']);
    error_log("Expire le: " . $utilisateur['reset_expire']);
    error_log("Maintenant: " . date('Y-m-d H:i:s'));
    error_log("Token match: " . ($utilisateur['reset_token'] === $token ? 'OUI' : 'NON'));
    error_log("Pas expiré: " . ($utilisateur['reset_expire'] > date('Y-m-d H:i:s') ? 'OUI' : 'NON'));
} else {
    error_log("Aucun utilisateur trouvé avec ce token");

    // Vérifier s'il y a des tokens en base
    $allTokens = $pdo->query("SELECT id, email, reset_token, reset_expire FROM utilisateurs WHERE reset_token IS NOT NULL")->fetchAll();
    error_log("Tokens en base: " . print_r($allTokens, true));
}

// Vérifier si le token existe et n'est pas expiré
if (!$utilisateur) {
    $errorMessage = "Token introuvable dans la base de données.";
} elseif ($utilisateur['reset_expire'] <= date('Y-m-d H:i:s')) {
    $errorMessage = "Token expiré. Demandez un nouveau lien de réinitialisation.";
} else {
    $errorMessage = null; // Token valide
}

if ($errorMessage) {
?>
    <!DOCTYPE html>
    <html lang="fr">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Erreur - Token invalide</title>
        <style>
            body {
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #1e3c72, #2a5298);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
                margin: 0;
            }

            .container {
                background: white;
                padding: 2rem;
                border-radius: 15px;
                text-align: center;
                max-width: 500px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .error-icon {
                font-size: 4rem;
                margin-bottom: 1rem;
            }

            h1 {
                color: #dc2626;
                margin-bottom: 1rem;
            }

            p {
                color: #6b7280;
                margin-bottom: 2rem;
                line-height: 1.6;
            }

            .btn {
                display: inline-block;
                padding: 0.75rem 1.5rem;
                background: #1e90ff;
                color: white;
                text-decoration: none;
                border-radius: 8px;
                font-weight: 600;
                transition: background 0.2s;
            }

            .btn:hover {
                background: #1565c0;
            }

            .debug {
                background: #f3f4f6;
                padding: 1rem;
                border-radius: 8px;
                margin-top: 1rem;
                font-size: 0.8rem;
                text-align: left;
                font-family: monospace;
            }
        </style>
    </head>

    <body>
        <div class="container">
            <div class="error-icon">❌</div>
            <h1>Token invalide ou expiré</h1>
            <p><?= htmlspecialchars($errorMessage) ?></p>
            <a href="motdepasse_oublie.php" class="btn">Demander un nouveau lien</a>

            <div class="debug">
                <strong>Informations de debug :</strong><br>
                Token reçu: <?= htmlspecialchars($token) ?><br>
                Longueur: <?= strlen($token) ?> caractères<br>
                <?php if ($utilisateur): ?>
                    Utilisateur: <?= htmlspecialchars($utilisateur['email']) ?><br>
                    Expire le: <?= htmlspecialchars($utilisateur['reset_expire']) ?><br>
                    Maintenant: <?= date('Y-m-d H:i:s') ?><br>
                <?php endif; ?>
            </div>
        </div>
    </body>

    </html>
<?php
    exit;
}

$erreur = "";
$message = "";

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nouveau_mdp = $_POST['nouveau_mdp'] ?? '';
    $conf_mdp = $_POST['conf_mdp'] ?? '';

    if (empty($nouveau_mdp) || empty($conf_mdp)) {
        $erreur = "Veuillez remplir tous les champs.";
    } elseif ($nouveau_mdp !== $conf_mdp) {
        $erreur = "Les mots de passe ne correspondent pas.";
    } elseif (strlen($nouveau_mdp) < 6) {
        $erreur = "Le mot de passe doit contenir au moins 6 caractères.";
    } else {
        try {
            $hash = password_hash($nouveau_mdp, PASSWORD_DEFAULT);
            $update = $pdo->prepare("UPDATE utilisateurs SET motdepasse = ?, reset_token = NULL, reset_expire = NULL WHERE id = ?");

            if ($update->execute([$hash, $utilisateur['id']])) {
                $message = "Votre mot de passe a été réinitialisé avec succès.";
                error_log("Mot de passe réinitialisé pour l'utilisateur: " . $utilisateur['email']);
            } else {
                $erreur = "Erreur lors de la mise à jour du mot de passe.";
            }
        } catch (Exception $e) {
            error_log("Erreur lors de la réinitialisation: " . $e->getMessage());
            $erreur = "Erreur système lors de la réinitialisation.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialisation mot de passe - Cyber Parc Djerba</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        h1 {
            color: #1e3c72;
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            text-align: center;
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 0.95rem;
        }

        .user-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }

        input[type="password"] {
            width: 100%;
            padding: 0.875rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        input[type="password"]:focus {
            outline: none;
            border-color: #1e90ff;
            box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.1);
        }

        .password-requirements {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #1e90ff, #1e3c72);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(30, 144, 255, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .alert {
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .success-actions {
            text-align: center;
            margin-top: 1.5rem;
        }

        .success-actions a {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #16a34a;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: background 0.2s;
        }

        .success-actions a:hover {
            background: #15803d;
        }

        .back-link {
            text-align: center;
            margin-top: 1.5rem;
        }

        .back-link a {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.2s ease;
        }

        .back-link a:hover {
            color: #1e90ff;
        }

        @media (max-width: 480px) {
            .container {
                padding: 2rem 1.5rem;
            }

            h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo">
            <div class="logo-icon">🔒</div>
            <h1>Nouveau mot de passe</h1>
            <p class="subtitle">Créez un nouveau mot de passe sécurisé</p>
        </div>

        <div class="user-info">
            <strong>👤 <?= htmlspecialchars($utilisateur['nom']) ?></strong><br>
            <small><?= htmlspecialchars($utilisateur['email']) ?></small>
        </div>

        <?php if ($erreur): ?>
            <div class="alert alert-error">
                <span>⚠️</span>
                <?= htmlspecialchars($erreur) ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <span>✅</span>
                <?= htmlspecialchars($message) ?>
            </div>
            <div class="success-actions">
                <a href="connexion.php">🚪 Se connecter maintenant</a>
            </div>
            <script>
                // Redirection automatique après 3 secondes
                setTimeout(() => {
                    window.location.href = 'connexion.php';
                }, 3000);
            </script>
        <?php else: ?>
            <form method="post">
                <div class="form-group">
                    <label for="nouveau_mdp">Nouveau mot de passe</label>
                    <input type="password" id="nouveau_mdp" name="nouveau_mdp" required
                        minlength="6" placeholder="Votre nouveau mot de passe">
                    <div class="password-requirements">
                        Minimum 6 caractères
                    </div>
                </div>

                <div class="form-group">
                    <label for="conf_mdp">Confirmer le mot de passe</label>
                    <input type="password" id="conf_mdp" name="conf_mdp" required
                        minlength="6" placeholder="Confirmez votre mot de passe">
                </div>

                <button type="submit" class="btn">
                    🔒 Réinitialiser le mot de passe
                </button>
            </form>
        <?php endif; ?>

        <div class="back-link">
            <a href="connexion.php">← Retour à la connexion</a>
        </div>
    </div>

    <script>
        // Validation côté client
        document.getElementById('conf_mdp').addEventListener('input', function() {
            const password = document.getElementById('nouveau_mdp').value;
            const confirm = this.value;

            if (confirm && password !== confirm) {
                this.setCustomValidity('Les mots de passe ne correspondent pas');
                this.style.borderColor = '#dc2626';
            } else {
                this.setCustomValidity('');
                this.style.borderColor = '#e5e7eb';
            }
        });

        // Afficher/masquer le mot de passe
        document.addEventListener('keydown', function(e) {
            if (e.altKey && e.key === 'v') {
                const inputs = document.querySelectorAll('input[type="password"]');
                inputs.forEach(input => {
                    input.type = input.type === 'password' ? 'text' : 'password';
                });
            }
        });
    </script>
</body>

</html>