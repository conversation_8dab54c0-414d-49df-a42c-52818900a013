<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
  die("Méthode non autorisée.");
}

try {
  // Vérification des champs obligatoires
  if (
    empty($_POST['entreprise_id']) ||
    empty($_POST['facture_id']) ||
    empty($_POST['numFact']) ||
    empty($_POST['trimestre']) ||
    empty($_POST['annee']) ||
    empty($_POST['Montant'])
  ) {
    die("Veuillez remplir tous les champs obligatoires.");
  }

  $entreprise_id = intval($_POST['entreprise_id']);
  $facture_id = intval($_POST['facture_id']);
  $numFact = intval($_POST['numFact']);
  $trimestre = $_POST['trimestre'];
  $annee = intval($_POST['annee']);
  $montant = floatval($_POST['Montant']);

  // Validation des données
  if ($entreprise_id <= 0) {
    die("ID d'entreprise invalide.");
  }

  if ($facture_id <= 0) {
    die("ID de facture invalide.");
  }

  if ($numFact <= 0) {
    die("Numéro de facture invalide.");
  }

  if ($montant <= 0) {
    die("Le montant doit être supérieur à 0.");
  }

  if (!in_array($trimestre, ['T1', 'T2', 'T3', 'T4'])) {
    die("Trimestre invalide.");
  }

  if ($annee < 1920 || $annee > 2030) {
    die("Année invalide.");
  }

  // Calculer les dates de début et fin selon le trimestre
  switch ($trimestre) {
    case 'T1':
      $dateDebut = "$annee-01-01";
      $dateFin = "$annee-03-31";
      break;
    case 'T2':
      $dateDebut = "$annee-04-01";
      $dateFin = "$annee-06-30";
      break;
    case 'T3':
      $dateDebut = "$annee-07-01";
      $dateFin = "$annee-09-30";
      break;
    case 'T4':
      $dateDebut = "$annee-10-01";
      $dateFin = "$annee-12-31";
      break;
  }

  // Connexion à la base de données
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  // Vérifier si l'entreprise existe et est active
  $checkEntreprise = $pdo->prepare("SELECT id, nom, active FROM entreprises WHERE id = ?");
  $checkEntreprise->execute([$entreprise_id]);
  $entreprise = $checkEntreprise->fetch(PDO::FETCH_ASSOC);

  if (!$entreprise) {
    die("Entreprise non trouvée.");
  }

  if ($entreprise['active'] != 1) {
    die("Impossible de modifier une facture d'une entreprise inactive.");
  }

  // Vérifier si la facture existe
  $checkFacture = $pdo->prepare("SELECT id FROM facture WHERE id = ? AND entreprise_id = ?");
  $checkFacture->execute([$facture_id, $entreprise_id]);
  if (!$checkFacture->fetch()) {
    die("Facture non trouvée.");
  }

  // Vérifier si le numéro de facture existe déjà pour cette entreprise (sauf pour la facture actuelle)
  $checkNumero = $pdo->prepare("SELECT id FROM facture WHERE numFact = ? AND entreprise_id = ? AND id != ?");
  $checkNumero->execute([$numFact, $entreprise_id, $facture_id]);
  if ($checkNumero->fetch()) {
    die("Ce numéro de facture existe déjà pour cette entreprise.");
  }

  // Vérifier si une facture existe déjà pour ce trimestre et cette année (sauf pour la facture actuelle)
  $checkTrimestre = $pdo->prepare("SELECT id FROM facture WHERE entreprise_id = ? AND dateDebut = ? AND dateFin = ? AND id != ?");
  $checkTrimestre->execute([$entreprise_id, $dateDebut, $dateFin, $facture_id]);
  if ($checkTrimestre->fetch()) {
    die("Une facture existe déjà pour le $trimestre $annee de cette entreprise.");
  }

  // Mettre à jour la facture
  $stmt = $pdo->prepare("UPDATE facture SET numFact = ?, dateDebut = ?, dateFin = ?, Montant = ? WHERE id = ? AND entreprise_id = ?");

  $result = $stmt->execute([$numFact, $dateDebut, $dateFin, $montant, $facture_id, $entreprise_id]);

  if ($result) {
    error_log("Facture modifiée - ID: {$facture_id}, Entreprise: {$entreprise['nom']}, Trimestre: {$trimestre} {$annee}, Montant: {$montant} DT");

    header("Location: details_entreprise.php?id={$entreprise_id}&success=facture_modifiee");
    exit;
  } else {
    die("Erreur lors de la modification de la facture.");
  }
} catch (PDOException $e) {
  error_log("Erreur BDD modification facture: " . $e->getMessage());
  die("Erreur base de données : " . $e->getMessage());
} catch (Exception $e) {
  error_log("Erreur générale modification facture: " . $e->getMessage());
  die("Erreur système : " . $e->getMessage());
}
?>
