# 🏢 CyberParc Management System

## Description
Application web de gestion de cyber parc permettant de gérer les entreprises, factures, virements et notifications.

## 🚀 Installation Rapide

### Méthode 1 : Installation Automatique (Recommandée)
1. Téléchargez et extrayez l'application sur votre serveur web
2. Ouvrez votre navigateur et allez sur : `http://votre-domaine.com/cyberparc-app/install.php`
3. Suivez les instructions à l'écran

### Méthode 2 : Installation Manuelle
Consultez le fichier `INSTALLATION.md` pour les instructions détaillées.

## 📋 Prérequis
- PHP 7.4+
- MySQL 5.7+ ou MariaDB 10.2+
- Serveur web (Apache/Nginx)
- Extensions PHP : PDO, PDO_MySQL, mbstring

## 🔧 Configuration
Après installation, vous pouvez :
- Gérer les entreprises du cyber parc
- Créer et suivre les factures
- Gérer les virements
- Recevoir des notifications automatiques

## 📁 Structure des Dossiers
```
cyberparc-app/
├── config/          # Configuration de l'application
├── pages/           # Pages de l'application
├── assets/          # Ressources (CSS, JS, images)
├── includes/        # Fichiers inclus
├── database/        # Scripts de base de données
├── vendor/          # Dépendances (après composer install)
├── index.php        # Point d'entrée principal
└── install.php      # Script d'installation
```

## 🛠️ Maintenance
- Effectuez des sauvegardes régulières de la base de données
- Gardez PHP et MySQL à jour
- Supprimez le fichier `install.php` après installation

## 📞 Support
Pour toute question, consultez le fichier `INSTALLATION.md` ou contactez votre administrateur système.

---
**Version** : 1.0.0  
**Développé pour** : Gestion de cyber parcs
