<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
  die("Méthode non autorisée.");
}

try {
  // Vérification des paramètres
  if (empty($_POST['id']) || empty($_POST['action'])) {
    die("Paramètres manquants.");
  }

  $id = intval($_POST['id']);
  $action = $_POST['action'];

  if ($id <= 0) {
    die("ID d'entreprise invalide.");
  }

  if (!in_array($action, ['activer', 'desactiver'])) {
    die("Action invalide.");
  }

  // Connexion à la base de données
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  // Vérifier si l'entreprise existe
  $checkStmt = $pdo->prepare("SELECT id, nom, active FROM entreprises WHERE id = ?");
  $checkStmt->execute([$id]);
  $entreprise = $checkStmt->fetch(PDO::FETCH_ASSOC);

  if (!$entreprise) {
    die("Entreprise non trouvée.");
  }

  // Déterminer le nouvel état
  $nouvelEtat = ($action === 'activer') ? 1 : 0;
  $ancienEtat = $entreprise['active'];

  // Vérifier si le changement est nécessaire
  if ($ancienEtat == $nouvelEtat) {
    $message = ($nouvelEtat == 1) ? "L'entreprise est déjà active." : "L'entreprise est déjà inactive.";
    header("Location: accueil.php?info=" . urlencode($message));
    exit;
  }

  // Mettre à jour le statut
  $stmt = $pdo->prepare("UPDATE entreprises SET active = ?, updated_at = NOW() WHERE id = ?");
  $result = $stmt->execute([$nouvelEtat, $id]);

  if ($result) {
    // Log de l'activité
    $actionText = ($nouvelEtat == 1) ? 'activée' : 'désactivée';
    error_log("Entreprise {$actionText} - ID: {$id}, Nom: {$entreprise['nom']}");

    // Message de succès
    $message = "L'entreprise '{$entreprise['nom']}' a été {$actionText} avec succès.";
    header("Location: accueil.php?success=" . urlencode($message));
    exit;
  } else {
    die("Erreur lors du changement de statut de l'entreprise.");
  }
} catch (PDOException $e) {
  error_log("Erreur BDD changement statut entreprise: " . $e->getMessage());
  die("Erreur base de données : " . $e->getMessage());
} catch (Exception $e) {
  error_log("Erreur générale changement statut entreprise: " . $e->getMessage());
  die("Erreur système : " . $e->getMessage());
}