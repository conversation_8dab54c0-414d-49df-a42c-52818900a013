# CyberParc Management System - Configuration Apache

# Activer la réécriture d'URL
RewriteEngine On

# Rediriger vers HTTPS (décommentez si vous avez SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Protéger les fichiers sensibles
<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# Protéger le dossier config
<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

# Protéger le dossier database
<Directory "database">
    Order allow,deny
    Deny from all
</Directory>

# Empêcher l'accès aux fichiers de sauvegarde
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Empêcher l'affichage du contenu des dossiers
Options -Indexes

# Configuration de sécurité
<IfModule mod_headers.c>
    # Sécurité XSS
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Cache pour les ressources statiques
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
</IfModule>

# Configuration PHP
<IfModule mod_php7.c>
    # Masquer la version PHP
    php_flag expose_php off
    
    # Limites d'upload
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value memory_limit 256M
</IfModule>

# Pages d'erreur personnalisées (optionnel)
# ErrorDocument 404 /cyberparc-app/pages/404.php
# ErrorDocument 500 /cyberparc-app/pages/500.php
