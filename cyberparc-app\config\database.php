<?php
/**
 * Configuration de la base de données pour CyberParc
 * Modifiez ces paramètres selon votre environnement
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'stage');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8');

// Configuration de l'application
define('APP_NAME', 'CyberParc Management');
define('APP_VERSION', '1.0.0');

// Configuration des chemins
define('BASE_PATH', dirname(__DIR__));
define('ASSETS_PATH', BASE_PATH . '/assets');
define('PAGES_PATH', BASE_PATH . '/pages');
define('INCLUDES_PATH', BASE_PATH . '/includes');

// Configuration de sécurité
define('SESSION_TIMEOUT', 3600); // 1 heure en secondes

// Configuration email (pour la récupération de mot de passe)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // À configurer
define('SMTP_PASSWORD', ''); // À configurer
define('SMTP_FROM_EMAIL', ''); // À configurer
define('SMTP_FROM_NAME', 'CyberParc Management');

/**
 * Fonction pour obtenir la connexion PDO
 */
function getPDOConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die("Erreur de connexion à la base de données : " . $e->getMessage());
        }
    }
    
    return $pdo;
}

/**
 * Fonction pour vérifier si l'utilisateur est connecté
 */
function isLoggedIn() {
    return isset($_SESSION['utilisateur']) && !empty($_SESSION['utilisateur']['id']);
}

/**
 * Fonction pour rediriger vers la page de connexion
 */
function redirectToLogin() {
    header('Location: /cyberparc-app/login.php');
    exit;
}

/**
 * Fonction pour sécuriser les données d'entrée
 */
function sanitizeInput($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}
