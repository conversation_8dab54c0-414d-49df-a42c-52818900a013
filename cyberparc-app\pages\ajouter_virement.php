<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
  die("Méthode non autorisée.");
}

try {
  // Vérification des champs obligatoires
  if (empty($_POST['entreprise_id']) || empty($_POST['numVirem']) || empty($_POST['typePai']) || empty($_POST['Montant'])) {
    die("Veuillez remplir tous les champs obligatoires.");
  }

  $entreprise_id = intval($_POST['entreprise_id']);
  $numVirem = intval($_POST['numVirem']);
  $typePai = trim($_POST['typePai']);
  $montant = floatval($_POST['Montant']);
  $fichierRecu = null;

  // Validation des données
  if ($entreprise_id <= 0) {
    die("ID d'entreprise invalide.");
  }

  if ($numVirem <= 0) {
    die("Numéro de virement invalide.");
  }

  if ($montant <= 0) {
    die("Le montant doit être supérieur à 0.");
  }

  // Connexion à la base de données
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  // Vérifier si l'entreprise existe et est active
  $checkEntreprise = $pdo->prepare("SELECT id, nom, active FROM entreprises WHERE id = ?");
  $checkEntreprise->execute([$entreprise_id]);
  $entreprise = $checkEntreprise->fetch(PDO::FETCH_ASSOC);

  if (!$entreprise) {
    die("Entreprise non trouvée.");
  }

  if ($entreprise['active'] != 1) {
    die("Impossible d'ajouter un virement à une entreprise inactive.");
  }

  // Vérifier si le numéro de virement existe déjà pour cette entreprise
  $checkNumero = $pdo->prepare("SELECT id FROM virement WHERE numVirem = ? AND entreprise_id = ?");
  $checkNumero->execute([$numVirem, $entreprise_id]);
  if ($checkNumero->fetch()) {
    die("Ce numéro de virement existe déjà pour cette entreprise.");
  }

  // Gestion de l'upload du reçu
  if (isset($_FILES['recu']) && $_FILES['recu']['error'] !== UPLOAD_ERR_NO_FILE) {
    $uploadDir = 'uploads/recus/';

    if (!file_exists($uploadDir)) {
      mkdir($uploadDir, 0755, true);
    }

    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx'];
    $allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    $maxSize = 5242880; // 5MB

    $extension = strtolower(pathinfo($_FILES['recu']['name'], PATHINFO_EXTENSION));

    if ($_FILES['recu']['error'] !== UPLOAD_ERR_OK) {
      $errors = [
        UPLOAD_ERR_INI_SIZE => 'Fichier trop volumineux (limite serveur)',
        UPLOAD_ERR_FORM_SIZE => 'Fichier trop volumineux (limite formulaire)',
        UPLOAD_ERR_PARTIAL => 'Upload partiel',
        UPLOAD_ERR_NO_TMP_DIR => 'Dossier temporaire manquant',
        UPLOAD_ERR_CANT_WRITE => 'Erreur d\'écriture',
        UPLOAD_ERR_EXTENSION => 'Extension bloquée'
      ];
      die($errors[$_FILES['recu']['error']] ?? 'Erreur d\'upload inconnue');
    }

    if (!in_array($extension, $allowedExtensions)) {
      die("Type de fichier non autorisé. Extensions acceptées: " . implode(', ', $allowedExtensions));
    }

    if ($_FILES['recu']['size'] > $maxSize) {
      die("Reçu trop volumineux (max 5MB)");
    }

    if (function_exists('finfo_open')) {
      $finfo = finfo_open(FILEINFO_MIME_TYPE);
      $mimeType = finfo_file($finfo, $_FILES['recu']['tmp_name']);
      finfo_close($finfo);

      if (!in_array($mimeType, $allowedMimeTypes)) {
        die("Type de fichier non autorisé (MIME: {$mimeType})");
      }
    }

    $fichierRecu = 'recu_' . $entreprise_id . '_' . time() . '_' . uniqid() . '.' . $extension;
    $targetPath = $uploadDir . $fichierRecu;

    if (!move_uploaded_file($_FILES['recu']['tmp_name'], $targetPath)) {
      die("Erreur lors de la sauvegarde du reçu");
    }
  }

  // Vérifier si la colonne date_virement existe
  $columns = $pdo->query("SHOW COLUMNS FROM virement")->fetchAll(PDO::FETCH_COLUMN);
  $hasDateVirement = in_array('date_virement', $columns);

  // Insertion
  if ($hasDateVirement) {
    $stmt = $pdo->prepare("INSERT INTO virement (numVirem, typePai, Montant, fichierRecu, entreprise_id, date_virement) VALUES (?, ?, ?, ?, ?, NOW())");
    $result = $stmt->execute([$numVirem, $typePai, $montant, $fichierRecu, $entreprise_id]);
  } else {
    $stmt = $pdo->prepare("INSERT INTO virement (numVirem, typePai, Montant, fichierRecu, entreprise_id) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute([$numVirem, $typePai, $montant, $fichierRecu, $entreprise_id]);
  }

  if ($result) {
    $virementId = $pdo->lastInsertId();
    error_log("Nouveau virement ajouté - ID: {$virementId}, Entreprise: {$entreprise['nom']}, Montant: {$montant} DT, Type: {$typePai}");
    header("Location: details_entreprise.php?id={$entreprise_id}&success=virement_ajoute");
    exit;
  } else {
    if ($fichierRecu && file_exists($uploadDir . $fichierRecu)) {
      unlink($uploadDir . $fichierRecu);
    }
    die("Erreur lors de l'ajout du virement.");
  }
} catch (PDOException $e) {
  if (isset($fichierRecu) && $fichierRecu && file_exists('uploads/recus/' . $fichierRecu)) {
    unlink('uploads/recus/' . $fichierRecu);
  }

  error_log("Erreur BDD ajout virement: " . $e->getMessage());
  die("Erreur base de données : " . $e->getMessage());
} catch (Exception $e) {
  if (isset($fichierRecu) && $fichierRecu && file_exists('uploads/recus/' . $fichierRecu)) {
    unlink('uploads/recus/' . $fichierRecu);
  }

  error_log("Erreur générale ajout virement: " . $e->getMessage());
  die("Erreur système : " . $e->getMessage());
}