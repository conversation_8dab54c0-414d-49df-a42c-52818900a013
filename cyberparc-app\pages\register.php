<?php
session_start();

// Inclure la configuration
require_once '../config/database.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = sanitizeInput($_POST['nom'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $telephone = sanitizeInput($_POST['telephone'] ?? '');
    $gouvernorat = sanitizeInput($_POST['gouvernorat'] ?? '');
    $local = sanitizeInput($_POST['local'] ?? '');
    
    // Validation
    if (empty($nom) || empty($email) || empty($password) || empty($telephone) || empty($gouvernorat) || empty($local)) {
        $error = 'Veuillez remplir tous les champs.';
    } elseif ($password !== $confirm_password) {
        $error = 'Les mots de passe ne correspondent pas.';
    } elseif (strlen($password) < 6) {
        $error = 'Le mot de passe doit contenir au moins 6 caractères.';
    } else {
        try {
            $pdo = getPDOConnection();
            
            // Vérifier si l'email existe déjà
            $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $error = 'Cette adresse email est déjà utilisée.';
            } else {
                // Créer le compte
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO utilisateurs (nom, email, motdepasse, telephone, gouvernorat, local) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$nom, $email, $hashedPassword, $telephone, $gouvernorat, $local]);
                
                header('Location: ../index.php?success=registration');
                exit;
            }
        } catch (Exception $e) {
            $error = 'Erreur lors de la création du compte. Veuillez réessayer.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= APP_NAME ?> - Inscription</title>
    <link rel="icon" type="image/x-icon" href="../assets/images/icone.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .register-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 2rem;
            font-size: 1.6rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: 500;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="tel"],
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .links {
            text-align: center;
            margin-top: 1.5rem;
        }

        .links a {
            color: #667eea;
            text-decoration: none;
        }

        .links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .register-container {
                margin: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">
            <img src="../assets/images/logo1.jpg" alt="Logo CyberParc" onerror="this.style.display='none'">
        </div>
        
        <h1>🏢 Créer un compte</h1>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                ❌ <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="nom">Nom complet :</label>
                <input type="text" id="nom" name="nom" required 
                       value="<?= htmlspecialchars($_POST['nom'] ?? '') ?>">
            </div>
            
            <div class="form-group">
                <label for="email">Email :</label>
                <input type="email" id="email" name="email" required 
                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password">Mot de passe :</label>
                    <input type="password" id="password" name="password" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirmer :</label>
                    <input type="password" id="confirm_password" name="confirm_password" required minlength="6">
                </div>
            </div>
            
            <div class="form-group">
                <label for="telephone">Téléphone :</label>
                <input type="tel" id="telephone" name="telephone" required 
                       value="<?= htmlspecialchars($_POST['telephone'] ?? '') ?>">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="gouvernorat">Gouvernorat :</label>
                    <select id="gouvernorat" name="gouvernorat" required>
                        <option value="">Sélectionner...</option>
                        <option value="Tunis" <?= ($_POST['gouvernorat'] ?? '') === 'Tunis' ? 'selected' : '' ?>>Tunis</option>
                        <option value="Ariana" <?= ($_POST['gouvernorat'] ?? '') === 'Ariana' ? 'selected' : '' ?>>Ariana</option>
                        <option value="Ben Arous" <?= ($_POST['gouvernorat'] ?? '') === 'Ben Arous' ? 'selected' : '' ?>>Ben Arous</option>
                        <option value="Manouba" <?= ($_POST['gouvernorat'] ?? '') === 'Manouba' ? 'selected' : '' ?>>Manouba</option>
                        <option value="Nabeul" <?= ($_POST['gouvernorat'] ?? '') === 'Nabeul' ? 'selected' : '' ?>>Nabeul</option>
                        <option value="Zaghouan" <?= ($_POST['gouvernorat'] ?? '') === 'Zaghouan' ? 'selected' : '' ?>>Zaghouan</option>
                        <option value="Bizerte" <?= ($_POST['gouvernorat'] ?? '') === 'Bizerte' ? 'selected' : '' ?>>Bizerte</option>
                        <option value="Béja" <?= ($_POST['gouvernorat'] ?? '') === 'Béja' ? 'selected' : '' ?>>Béja</option>
                        <option value="Jendouba" <?= ($_POST['gouvernorat'] ?? '') === 'Jendouba' ? 'selected' : '' ?>>Jendouba</option>
                        <option value="Kef" <?= ($_POST['gouvernorat'] ?? '') === 'Kef' ? 'selected' : '' ?>>Kef</option>
                        <option value="Siliana" <?= ($_POST['gouvernorat'] ?? '') === 'Siliana' ? 'selected' : '' ?>>Siliana</option>
                        <option value="Sousse" <?= ($_POST['gouvernorat'] ?? '') === 'Sousse' ? 'selected' : '' ?>>Sousse</option>
                        <option value="Monastir" <?= ($_POST['gouvernorat'] ?? '') === 'Monastir' ? 'selected' : '' ?>>Monastir</option>
                        <option value="Mahdia" <?= ($_POST['gouvernorat'] ?? '') === 'Mahdia' ? 'selected' : '' ?>>Mahdia</option>
                        <option value="Sfax" <?= ($_POST['gouvernorat'] ?? '') === 'Sfax' ? 'selected' : '' ?>>Sfax</option>
                        <option value="Kairouan" <?= ($_POST['gouvernorat'] ?? '') === 'Kairouan' ? 'selected' : '' ?>>Kairouan</option>
                        <option value="Kasserine" <?= ($_POST['gouvernorat'] ?? '') === 'Kasserine' ? 'selected' : '' ?>>Kasserine</option>
                        <option value="Sidi Bouzid" <?= ($_POST['gouvernorat'] ?? '') === 'Sidi Bouzid' ? 'selected' : '' ?>>Sidi Bouzid</option>
                        <option value="Gabès" <?= ($_POST['gouvernorat'] ?? '') === 'Gabès' ? 'selected' : '' ?>>Gabès</option>
                        <option value="Médenine" <?= ($_POST['gouvernorat'] ?? '') === 'Médenine' ? 'selected' : '' ?>>Médenine</option>
                        <option value="Tataouine" <?= ($_POST['gouvernorat'] ?? '') === 'Tataouine' ? 'selected' : '' ?>>Tataouine</option>
                        <option value="Gafsa" <?= ($_POST['gouvernorat'] ?? '') === 'Gafsa' ? 'selected' : '' ?>>Gafsa</option>
                        <option value="Tozeur" <?= ($_POST['gouvernorat'] ?? '') === 'Tozeur' ? 'selected' : '' ?>>Tozeur</option>
                        <option value="Kebili" <?= ($_POST['gouvernorat'] ?? '') === 'Kebili' ? 'selected' : '' ?>>Kebili</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="local">Nom du local :</label>
                    <input type="text" id="local" name="local" required 
                           value="<?= htmlspecialchars($_POST['local'] ?? '') ?>">
                </div>
            </div>
            
            <button type="submit" class="btn">Créer le compte</button>
        </form>
        
        <div class="links">
            <a href="../index.php">← Retour à la connexion</a>
        </div>
    </div>
</body>
</html>
