<?php
$pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$id = $_POST['entreprise_id'];
$factures = $_POST['factures'];
$virements = $_POST['virements'];
$remarques = $_POST['remarques'];

$check = $pdo->prepare("SELECT * FROM comptabilite WHERE entreprise_id = ?");
$check->execute([$id]);

if ($check->rowCount() > 0) {
  $stmt = $pdo->prepare("UPDATE comptabilite SET factures = ?, virements = ?, remarques = ? WHERE entreprise_id = ?");
  $stmt->execute([$factures, $virements, $remarques, $id]);
} else {
  $stmt = $pdo->prepare("INSERT INTO comptabilite (entreprise_id, factures, virements, remarques) VALUES (?, ?, ?, ?)");
  $stmt->execute([$id, $factures, $virements, $remarques]);
}

header("Location: details_entreprise.php?id=$id");
exit;